import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'

import { useToast } from '../useToast'

describe('useToast', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should add success toast', () => {
    const { result } = renderHook(() => useToast())

    act(() => {
      result.current.success({ message: 'Success message' })
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0]).toMatchObject({
      type: 'success',
      message: 'Success message',
    })
    expect(result.current.toasts[0]).toHaveProperty('id')
  })

  it('should add error toast', () => {
    const { result } = renderHook(() => useToast())

    act(() => {
      result.current.error({ message: 'Error message' })
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0]).toMatchObject({
      type: 'error',
      message: 'Error message',
    })
  })

  it('should add info toast', () => {
    const { result } = renderHook(() => useToast())

    act(() => {
      result.current.info({ message: 'Info message' })
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0]).toMatchObject({
      type: 'info',
      message: 'Info message',
    })
  })

  it('should remove toast manually', () => {
    const { result } = renderHook(() => useToast())

    let toastId
    act(() => {
      toastId = result.current.success({ message: 'Test message' })
    })

    expect(result.current.toasts).toHaveLength(1)

    act(() => {
      result.current.removeToast(toastId)
    })

    expect(result.current.toasts).toHaveLength(0)
  })

  it('should auto-remove toast after 5 seconds', () => {
    const { result } = renderHook(() => useToast())

    act(() => {
      result.current.success({ message: 'Test message' })
    })

    expect(result.current.toasts).toHaveLength(1)

    act(() => {
      vi.advanceTimersByTime(5000)
    })

    expect(result.current.toasts).toHaveLength(0)
  })

  it('should handle multiple toasts', () => {
    const { result } = renderHook(() => useToast())

    act(() => {
      result.current.success({ message: 'Success 1' })
      result.current.error({ message: 'Error 1' })
      result.current.info({ message: 'Info 1' })
    })

    expect(result.current.toasts).toHaveLength(3)
    expect(result.current.toasts[0].message).toBe('Success 1')
    expect(result.current.toasts[1].message).toBe('Error 1')
    expect(result.current.toasts[2].message).toBe('Info 1')
  })
})
