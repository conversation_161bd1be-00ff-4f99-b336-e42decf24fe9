import { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import styles from './Header.module.scss'
import { useShallowEqualSelector } from '../../../customHooks/useShallowEqualSelector'
import { setCityOptions, setEventsOptions } from '../../../mocks/calendarOptions'
import { Operation as DataOperation } from '../../../reducer/data/data'
import { getEvents } from '../../../reducer/data/selectors'
import { ActionCreator, Operation, screens } from '../../../reducer/memberRegistration/registration'
import { ActionCreator as ActionCreatorMemReg } from '../../../reducer/memberRegistration/registration'
import { getIsOpenReservedNumbers, getIsOpenStat, getScreen } from '../../../reducer/memberRegistration/selectors'
import { getThemeMemReg } from '../../../reducer/theme/selectors'
import { ActionCreator as ActionCreatorTheme } from '../../../reducer/theme/theme'
import { updateLocalStorage } from '../../../utils/utils'
import CustomSelect from '../../Calendar/Select/Select'

const Header = ({
  event,
  city,
  isEventSelected,
  onChangeEventSelected,
  onChangeCity,
  onChangeEvent,
  handleBackToSearch,
}) => {
  const dispatch = useDispatch()
  const theme = useSelector((state) => getThemeMemReg(state))
  const screen = useSelector((state) => getScreen(state))
  const isOpenStat = useSelector((state) => getIsOpenStat(state))
  const isOpenReservedNumbers = useSelector((state) => getIsOpenReservedNumbers(state))
  const events = useShallowEqualSelector((state) => getEvents(state))
  const [eventCities, setEventCities] = useState([])
  const effectRan = useRef(false)

  useEffect(() => {
    if (events.length === 0 && effectRan.current === false) {
      dispatch(DataOperation.loadEventType())
      effectRan.current = true
    }
  }, [events.length, dispatch])

  const handleEventReset = () => {
    onChangeEventSelected(false)
    onChangeEvent(null)
    onChangeCity(null)
    localStorage.removeItem(`memberRegEvent`)
    localStorage.removeItem(`memberRegCity`)
    dispatch(ActionCreator.setEventFormats([]))
    handleBackToSearch(screens.first)
  }

  const handleCityChange = (evt) => {
    const cityName = evt.label.split(` - `)[0]
    const cityId = evt.value.split(`##`)[0]
    const eventCityId = evt.value.split(`##`)[1]
    const findCity = eventCities.find((item) => item.public_id === eventCityId)

    let city = {
      value: cityId,
      label: cityName,
      eventCityId: eventCityId,
    }

    if ('qr' in findCity) {
      city.qr = findCity.qr
    }

    onChangeCity(city)
    updateLocalStorage(`memberRegCity`, `add`, city)
  }

  const handleEventChange = (evt) => {
    onChangeEvent(evt)

    const getEventCities = (event_id) => {
      return events.find((item) => item.public_id === event_id.value).event_city.map((item) => item)
    }

    const filteredCities = getEventCities(evt)

    setEventCities(filteredCities)
    onChangeCity(null)
    updateLocalStorage(`memberRegEvent`, `add`, evt)
  }

  const handleTheme = () => {
    if (theme) {
      document.body.classList.remove('white')
      localStorage.setItem('themeReg', 'dark')
      dispatch(ActionCreatorTheme.setThemeMemReg(false))
    } else if (!theme) {
      document.body.classList.add('white')
      localStorage.setItem('themeReg', 'light')
      dispatch(ActionCreatorTheme.setThemeMemReg(true))
    }
  }

  const handleOpenStatPopup = () => {
    if (isOpenStat) {
      dispatch(ActionCreatorMemReg.setIsOpenStat(false))
    } else {
      dispatch(ActionCreatorMemReg.setIsOpenStat(true))
      dispatch(Operation.loadStatProducts(event.value, city.value))
      dispatch(Operation.loadStatTickets(event.value, city.eventCityId))
    }

    if (isOpenReservedNumbers) {
      dispatch(ActionCreatorMemReg.setIsOpenReservedNumbers(false))
    }
  }

  const handleOpenReservedNumbers = () => {
    if (isOpenReservedNumbers) {
      dispatch(ActionCreatorMemReg.setIsOpenReservedNumbers(false))
    } else {
      dispatch(ActionCreatorMemReg.setIsOpenReservedNumbers(true))
      dispatch(Operation.loadReservedNumbers(city.eventCityId))
    }

    if (isOpenStat) {
      dispatch(ActionCreatorMemReg.setIsOpenStat(false))
    }
  }

  const getTitle = () => {
    if (screen === screens.result && !isOpenStat) {
      return `Результаты поиска`
    } else if (isOpenStat) {
      return `Статистика`
    } else if (isOpenReservedNumbers) {
      return `Зарезервированные номера`
    } else {
      return `Регистрация участника`
    }
  }

  return isEventSelected ? (
    <div className={`${styles.titleWrap} ${theme ? `${styles.titleWrapWhite}` : ``}`}>
      <div className={styles.titleContainer}>
        <h1 className={styles.title}>{getTitle()}</h1>
        {isEventSelected && event !== null && !isOpenStat && !isOpenReservedNumbers && (
          <h2 className={`${styles.title} ${styles.titleResetEvent}`}>
            <span>{`${event.label}, ${city.label}`}</span>

            <button onClick={handleEventReset} className={styles.btnResetEvent} type="button">
              Выбрать другое мероприятие
            </button>
          </h2>
        )}
      </div>

      <div className={styles.topBtns}>
        {isEventSelected && (
          <button className={`${styles.topBtn} ${styles.statBtn}`} onClick={handleOpenStatPopup} type="button">
            <span className="visually-hidden">Статистика</span>
          </button>
        )}

        {isEventSelected && (
          <button
            className={`${styles.topBtn} ${styles.reservedNumbersBtn}`}
            onClick={handleOpenReservedNumbers}
            type="button"
          >
            <span className="visually-hidden">Зарезервированные номера</span>
          </button>
        )}

        <div className={styles.charger}>
          <button className={styles.changerBtn} aria-label="Переключатель темы" onClick={handleTheme}>
            <div className={styles.changerRound} />
          </button>
        </div>
      </div>
    </div>
  ) : (
    <div className={styles.choiceEventWrap}>
      <CustomSelect
        disabled={``}
        handleSelectChange={handleEventChange}
        handleFocus={() => {}}
        error={``}
        value={event}
        prefix={`member-select`}
        styles={styles}
        title={`Мероприятие`}
        name={`event`}
        id={`event-t`}
        options={setEventsOptions(events)}
        placeholder={`Выберите мероприятие`}
        closeMenuOnScroll={() => false}
      />
      <CustomSelect
        disabled={event === null}
        handleSelectChange={handleCityChange}
        handleFocus={() => {}}
        error={``}
        prefix={`member-select`}
        styles={styles}
        title={`Город`}
        name={`city`}
        id={`city-t`}
        options={setCityOptions(eventCities)}
        placeholder={`Выберите город`}
        closeMenuOnScroll={() => false}
      />

      <button
        onClick={() => onChangeEventSelected(true)}
        type={`button`}
        className={`${styles.btnSearch} ${styles.btnChoiceEvent}`}
        disabled={city === null}
      >
        Продолжить
      </button>
    </div>
  )
}

export default Header
