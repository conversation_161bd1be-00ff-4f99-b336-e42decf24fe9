import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'

import Toast from '../Toast'

describe('Toast', () => {
  const mockOnRemove = vi.fn()
  const mockToast = {
    id: '1',
    type: 'success',
    message: 'Test message',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders toast with correct message', () => {
    render(<Toast toast={mockToast} onRemove={mockOnRemove} />)
    
    expect(screen.getByText('Test message')).toBeInTheDocument()
  })

  it('calls onRemove when close button is clicked', () => {
    render(<Toast toast={mockToast} onRemove={mockOnRemove} />)
    
    const closeButton = screen.getByRole('button', { name: /закрыть уведомление/i })
    fireEvent.click(closeButton)
    
    expect(mockOnRemove).toHaveBeenCalledWith('1')
  })

  it('applies correct CSS class for success type', () => {
    render(<Toast toast={mockToast} onRemove={mockOnRemove} />)
    
    const toastElement = screen.getByText('Test message').closest('.toast')
    expect(toastElement).toHaveClass('success')
  })

  it('applies correct CSS class for error type', () => {
    const errorToast = { ...mockToast, type: 'error' }
    render(<Toast toast={errorToast} onRemove={mockOnRemove} />)
    
    const toastElement = screen.getByText('Test message').closest('.toast')
    expect(toastElement).toHaveClass('error')
  })

  it('auto-removes toast after 5 seconds', async () => {
    vi.useFakeTimers()
    
    render(<Toast toast={mockToast} onRemove={mockOnRemove} />)
    
    // Fast-forward time by 5 seconds
    vi.advanceTimersByTime(5000)
    
    await waitFor(() => {
      expect(mockOnRemove).toHaveBeenCalledWith('1')
    })
    
    vi.useRealTimers()
  })
})
