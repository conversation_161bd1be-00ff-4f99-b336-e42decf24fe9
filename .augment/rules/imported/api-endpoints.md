---
type: "agent_requested"
---

## Организация файлов

- Каждый API запрос должен быть размещен в отдельном файле в директории `src/features/{entity}/api/`
- Имя файла должно отражать действие: `getEntity.js`, `createEntity.js`, `updateEntity.js`, `deleteEntity.js`
- Экспортировать константу с ключом запроса в главном файле получения данных (например, в `getEntityList.js`)

## Структура файла

- Файл должен содержать две части:

1. Функцию для выполнения API запроса (без префикса `use`)
2. Хук React Query для работы с этим запросом (с префиксом `use`)

## Функция API запроса

```js
const getEntity = (params) => {
  return axios.get(`${APIRoute.GET_ENTITY}`, { params })
}
```

## Хук React Query

- Для GET запросов использовать `useQuery`
- Для POST/PUT/DELETE запросов использовать `useMutation`
- Настраивать `staleTime` и `refetchOnWindowFocus` согласно требованиям

## Правила для useQuery

```js
export const useGetEntity = (id) => {
  return useQuery({
    queryKey: ['entity', id],
    queryFn: () => getEntity(id),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
```

## Правила для useMutation

```js
export const useCreateEntity = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: createEntity,
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['entities'])
        openToast.success({ message: 'Сущность создана!' })
      }
    },
  })
}
```

## Импорты

- Сгруппировать импорты в логические блоки:

1. React и React Query
2. Константы и утилиты проекта
3. Локальные импорты

## Использование в компонентах

- При импорте использовать деструктуризацию данных:

```js
const { data: entityResponse } = useGetEntity()
const entities = entityResponse?.data?.values || []
```

- Для мутаций использовать:

```js
const { mutate: createEntityMutation } = useCreateEntity()
```

- При обработке ответов всегда предусматривать дефолтные значения
